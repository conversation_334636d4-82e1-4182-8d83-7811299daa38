#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : mediation_case_query_serializers.py
<AUTHOR> JT_DA
@Date     : 2025/07/22
@File_Desc: 调解案件查询相关序列化器
"""

from rest_framework import serializers
from apps.mediation_management.models import MediationCase


class MediationCaseByDebtorSerializer(serializers.Serializer):
    """调解案件按债务人查询序列化器 - 用于验证债务人姓名和身份证号参数"""

    name = serializers.CharField(required=True, max_length=200, help_text="债务人姓名，用于查询对应的债务人对象")

    id_card = serializers.CharField(required=True, max_length=50, help_text="债务人身份证号，用于查询对应的债务人对象")


class MediationContentSerializer(serializers.Serializer):
    """调解信息内容序列化器 - 用于验证调解案件编号参数"""

    case_number = serializers.Char<PERSON>ield(
        required=True, max_length=50, help_text="调解案件编号，用于查询对应的调解案件对象"
    )

    def validate_case_number(self, value):
        """验证调解案件编号是否存在"""
        try:
            MediationCase.objects.get(case_number=value)
        except MediationCase.DoesNotExist:
            raise serializers.ValidationError("指定的调解案件不存在")
        return value


class MediationPlanConfigSerializer(serializers.Serializer):
    """调解方案配置序列化器 - 用于验证调解案件编号参数"""

    case_number = serializers.CharField(
        required=True, max_length=50, help_text="调解案件编号，用于查询对应的调解案件对象"
    )

    def validate_case_number(self, value):
        """验证调解案件编号是否存在"""
        try:
            MediationCase.objects.get(case_number=value)
        except MediationCase.DoesNotExist:
            raise serializers.ValidationError("指定的调解案件不存在")
        return value


class MediationCaseQuerySerializer(serializers.Serializer):
    """调解案件查询参数序列化器 - 用于验证微信用户调解案件查询参数"""

    mediation_case_number = serializers.CharField(
        required=False, max_length=50, help_text="调解案件号，可选参数，用于查询指定的调解案件"
    )

    def validate_mediation_case_number(self, value):
        """验证调解案件号是否存在"""
        if value is not None:
            try:
                MediationCase.objects.get(case_number=value)
            except MediationCase.DoesNotExist:
                raise serializers.ValidationError("指定的调解案件不存在")
        return value


class MediationCaseWechatSerializer(serializers.ModelSerializer):
    """微信端调解案件序列化器 - 用于微信小程序端返回简化的案件信息"""

    # 状态字段中文显示
    case_status_cn = serializers.CharField(source="get_case_status_display", read_only=True)

    # 时间字段格式化显示（仅显示日期）
    initiate_date = serializers.DateTimeField(format="%Y-%m-%d", read_only=True)

    # 调解进度信息
    mediation_progress = serializers.SerializerMethodField()

    # 结案日期（格式化为日期）
    close_date = serializers.DateTimeField(format="%Y-%m-%d", read_only=True)

    class Meta:
        model = MediationCase
        fields = [
            "id",  # 调解案件ID
            "case_number",  # 调解案件号
            "case_status",  # 案件状态
            "case_status_cn",  # 案件状态中文显示
            "initiate_date",  # 发起日期
            "mediation_progress",  # 调解进度信息
            "close_date",  # 结案日期
        ]

    def get_mediation_progress(self, obj):
        """
        获取调解进度信息

        根据案件状态和mediation_plan字段确定调解进度：
        - pending_confirm: 调解确认
        - in_progress + mediation_plan为空: 方案确认
        - in_progress + mediation_plan不为空: 协议签署
        - completed: 完成
        - 其他状态: 未知状态
        """
        if obj.case_status == "pending_confirm":
            # 案件状态为"待确认" → 调解进度："调解确认"
            return "调解确认"
        elif obj.case_status == "in_progress":
            if obj.mediation_plan is None:
                # 案件状态为"进行中"且mediation_plan为空 → 调解进度："方案确认"
                return "方案确认"
            else:
                # 案件状态为"进行中"且mediation_plan不为空 → 调解进度："协议签署"
                return "协议签署"
        elif obj.case_status == "completed":
            # 案件状态为"已完成" → 调解进度："完成"
            return "完成"
        else:
            # 其他状态（draft、initiated、closed）暂不在进度查询范围内
            return "未知状态"
